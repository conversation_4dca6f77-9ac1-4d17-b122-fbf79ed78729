// تغيير مسار ملف الأدوات ليكون صحيحًا
fetch('./unique_tools.json')
  .then(response => response.json())
  .then(data => {
    const container = document.getElementById('tools-container');
    const searchBox = document.getElementById('searchBox');

    // إزالة التكرارات من الأدوات
    const uniqueTools = [];
    const toolNames = new Set();
    
    data.forEach(tool => {
      if (!toolNames.has(tool.name)) {
        toolNames.add(tool.name);
        uniqueTools.push(tool);
      }
    });

    function displayTools(tools) {
      container.innerHTML = "";
      tools.forEach(tool => {
        const div = document.createElement('div');
        div.className = 'tool-card';
        
        // إضافة صورة افتراضية بدون الاعتماد على وجود صور خارجية
        div.innerHTML = `
          <div class="tool-icon">${tool.name.charAt(0)}</div>
          <h2>${tool.name}</h2>
          <p>${tool.description}</p>
          <code>${tool.command}</code><br><br>
          <a href="${tool.download}" target="_blank">📦 تحميل الأداة</a>
        `;
        container.appendChild(div);
      });
    }

    displayTools(uniqueTools);

    searchBox.addEventListener('input', () => {
      const keyword = searchBox.value.toLowerCase();
      const filtered = uniqueTools.filter(tool =>
        tool.name.toLowerCase().includes(keyword) ||
        tool.description.toLowerCase().includes(keyword)
      );
      displayTools(filtered);
    });
  })
  .catch(error => {
    console.error('خطأ في تحميل ملف الأدوات:', error);
    document.getElementById('tools-container').innerHTML = 
      '<div class="error-message">حدث خطأ في تحميل الأدوات. يرجى التحقق من وجود ملف unique_tools.json</div>';
  });

// إضافة وظيفة تسجيل الدخول المحدثة
document.addEventListener('DOMContentLoaded', function() {
  const loginBtn = document.getElementById('loginBtn');
  const loginModal = document.getElementById('loginModal');
  const closeBtn = document.querySelector('.close');
  const loginForm = document.getElementById('loginForm');
  const welcomeMessage = document.getElementById('welcomeMessage');

  // فتح نافذة تسجيل الدخول
  if (loginBtn) {
    loginBtn.addEventListener('click', function(e) {
      e.preventDefault();
      loginModal.style.display = 'block';
    });
  }

  // إغلاق النافذة
  if (closeBtn) {
    closeBtn.addEventListener('click', function() {
      loginModal.style.display = 'none';
    });
  }

  // إغلاق النافذة عند النقر خارجها
  window.addEventListener('click', function(e) {
    if (e.target === loginModal) {
      loginModal.style.display = 'none';
    }
  });

  // معالجة إرسال النموذج
  if (loginForm) {
    loginForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // جمع بيانات النموذج
      const formData = new FormData(loginForm);
      const userData = {
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        password: formData.get('password'),
        github: formData.get('github'),
        birthdate: formData.get('birthdate'),
        ambitions: formData.get('ambitions'),
        comments: formData.get('comments'),
        registrationDate: new Date().toISOString()
      };

      // حفظ البيانات في التخزين المحلي
      localStorage.setItem('userData', JSON.stringify(userData));
      localStorage.setItem('loggedIn', 'true');

      // إظهار رسالة الترحيب
      if (welcomeMessage) {
        welcomeMessage.style.display = 'block';
        setTimeout(() => {
          welcomeMessage.style.display = 'none';
        }, 5000);
      }

      // إغلاق النافذة
      loginModal.style.display = 'none';

      // تحديث زر تسجيل الدخول
      loginBtn.textContent = `مرحباً ${userData.fullName}`;
      loginBtn.style.backgroundColor = '#00cc7a';

      // إظهار رسالة نجاح
      alert('تم تسجيل الدخول بنجاح! مرحباً بك في ShadowSec Tools');

      // تحديث عداد الزوار
      updateVisitorCount();
    });
  }

  // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
  checkLoginStatus();

  // تحديث عداد الزوار
  updateVisitorCount();
});

// وظيفة التحقق من حالة تسجيل الدخول
function checkLoginStatus() {
  const isLoggedIn = localStorage.getItem('loggedIn');
  const userData = localStorage.getItem('userData');
  const loginBtn = document.getElementById('loginBtn');
  const welcomeMessage = document.getElementById('welcomeMessage');

  if (isLoggedIn === 'true' && userData) {
    const user = JSON.parse(userData);
    loginBtn.textContent = `مرحباً ${user.fullName}`;
    loginBtn.style.backgroundColor = '#00cc7a';

    // إظهار رسالة الترحيب للمستخدمين المسجلين
    if (welcomeMessage) {
      welcomeMessage.style.display = 'block';
    }
  }
}

// وظيفة تحديث عداد الزوار
function updateVisitorCount() {
  const visitorCountElement = document.getElementById('visitorCount');
  if (visitorCountElement) {
    // محاكاة عداد الزوار
    let currentCount = localStorage.getItem('visitorCount');
    if (!currentCount) {
      currentCount = Math.floor(Math.random() * 1000) + 1200; // رقم عشوائي بين 1200-2200
      localStorage.setItem('visitorCount', currentCount);
    } else {
      currentCount = parseInt(currentCount) + 1;
      localStorage.setItem('visitorCount', currentCount);
    }

    // تحديث العرض مع تنسيق الأرقام
    visitorCountElement.textContent = currentCount.toLocaleString('ar-SA');
  }
}
