// تغيير مسار ملف الأدوات ليكون صحيحًا
fetch('./unique_tools.json')
  .then(response => response.json())
  .then(data => {
    const container = document.getElementById('tools-container');
    const searchBox = document.getElementById('searchBox');

    // إزالة التكرارات من الأدوات
    const uniqueTools = [];
    const toolNames = new Set();
    
    data.forEach(tool => {
      if (!toolNames.has(tool.name)) {
        toolNames.add(tool.name);
        uniqueTools.push(tool);
      }
    });

    function displayTools(tools) {
      container.innerHTML = "";
      tools.forEach(tool => {
        const div = document.createElement('div');
        div.className = 'tool-card';
        
        // إضافة صورة افتراضية بدون الاعتماد على وجود صور خارجية
        div.innerHTML = `
          <div class="tool-icon">${tool.name.charAt(0)}</div>
          <h2>${tool.name}</h2>
          <p>${tool.description}</p>
          <code>${tool.command}</code><br><br>
          <a href="${tool.download}" target="_blank">📦 تحميل الأداة</a>
        `;
        container.appendChild(div);
      });
    }

    displayTools(uniqueTools);

    searchBox.addEventListener('input', () => {
      const keyword = searchBox.value.toLowerCase();
      const filtered = uniqueTools.filter(tool =>
        tool.name.toLowerCase().includes(keyword) ||
        tool.description.toLowerCase().includes(keyword)
      );
      displayTools(filtered);
    });
  })
  .catch(error => {
    console.error('خطأ في تحميل ملف الأدوات:', error);
    document.getElementById('tools-container').innerHTML = 
      '<div class="error-message">حدث خطأ في تحميل الأدوات. يرجى التحقق من وجود ملف unique_tools.json</div>';
  });

// إضافة وظيفة تسجيل الدخول
document.addEventListener('DOMContentLoaded', function() {
  const loginBtn = document.getElementById('loginBtn');
  const welcomeMessage = document.getElementById('welcomeMessage');
  
  if (loginBtn) {
    loginBtn.addEventListener('click', function(e) {
      e.preventDefault();
      
      // محاكاة نافذة تسجيل الدخول البسيطة
      const username = prompt('أدخل اسم المستخدم:');
      if (username) {
        const password = prompt('أدخل كلمة المرور:');
        if (password) {
          // محاكاة عملية تسجيل الدخول الناجحة
          window.location.href = 'profile.html';  // توجيه المستخدم إلى صفحة الملف الشخصي
          
          // تخزين حالة تسجيل الدخول
          localStorage.setItem('loggedIn', 'true');
          localStorage.setItem('username', username);
        }
      }
    });
  }
});
