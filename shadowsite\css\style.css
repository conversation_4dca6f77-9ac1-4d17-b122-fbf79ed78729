
body {
  font-family: 'Courier New', monospace;
  background-color: #0d0d0d;
  color: #00ff99;
  margin: 0;
  padding: 0;
}

header {
  background-color: #121212;
  text-align: center;
  padding: 25px 10px;
  border-bottom: 2px solid #00ff99;
}

header h1 {
  font-size: 2em;
  color: #00ffe7;
}

nav {
  margin-top: 10px;
}

nav a {
  color: #00ff99;
  margin: 0 15px;
  text-decoration: none;
}

#searchBox {
  margin-top: 20px;
  padding: 10px;
  width: 80%;
  max-width: 400px;
  border: none;
  border-radius: 5px;
  background-color: #1e1e1e;
  color: #00ff99;
  font-size: 16px;
}

/* إضافة نمط للأيقونة الافتراضية */
.tool-icon {
  width: 80px;
  height: 80px;
  background-color: #1e1e1e;
  color: #00ff99;
  font-size: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 auto 15px auto;
  border: 2px solid #00ff99;
}

.tool-card {
  background-color: #1a1a1a;
  margin: 20px auto;
  padding: 15px;
  max-width: 700px;
  border-radius: 10px;
  border: 1px solid #00ff99;
  box-shadow: 0 0 10px #00ff9933;
  transition: transform 0.2s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.tool-card:hover {
  transform: scale(1.02);
  box-shadow: 0 0 20px #00ff99aa;
}

.tool-card::before {
  content: "🔒";
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 20px;
  opacity: 0.7;
}

.tool-card img {
  width: 80px;
  float: right;
  margin-left: 15px;
}

.tool-card h2 {
  margin-top: 0;
}

.tool-card a {
  color: #00ffe7;
  text-decoration: underline;
}

/* أنماط زر تسجيل الدخول */
.login-btn {
  background-color: #00ff99;
  color: #121212 !important;
  padding: 8px 15px;
  border-radius: 5px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background-color: #00cc7a;
  transform: scale(1.05);
}

/* أنماط رسالة الترحيب */
.welcome-message {
  background-color: rgba(0, 255, 153, 0.2);
  color: #00ff99;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  border-left: 4px solid #00ff99;
}
